<script lang="ts">
  import { EntityImageInput } from "$lib/components";
  import type { Common } from "@commune/api";

  let file = $state<File | null>(null);
  let locale: Common.WebsiteLocale = "en";
  let disabled = $state(false);

  function handleLocaleChange(newLocale: Common.WebsiteLocale) {
    locale = newLocale;
  }

  function handleToggleDisabled() {
    disabled = !disabled;
  }

  function handleClearFile() {
    file = null;
  }
</script>

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-8 mx-auto">
      <h1 class="mb-4">Entity Image Input Component Test</h1>
      
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">Controls</h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <label class="form-label">Language</label>
              <select 
                class="form-select" 
                value={locale} 
                onchange={(e) => handleLocaleChange(e.target.value as Common.WebsiteLocale)}
              >
                <option value="en">English</option>
                <option value="ru">Русский</option>
              </select>
            </div>
            <div class="col-md-4">
              <label class="form-label">State</label>
              <div class="form-check">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  checked={disabled}
                  onchange={handleToggleDisabled}
                  id="disabledCheck"
                >
                <label class="form-check-label" for="disabledCheck">
                  Disabled
                </label>
              </div>
            </div>
            <div class="col-md-4">
              <label class="form-label">Actions</label>
              <div>
                <button 
                  type="button" 
                  class="btn btn-outline-secondary btn-sm"
                  onclick={handleClearFile}
                >
                  Clear File
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">Image Input Component</h5>
        </div>
        <div class="card-body">
          <EntityImageInput 
            bind:file={file}
            {locale}
            {disabled}
          />
        </div>
      </div>

      {#if file}
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">File Information</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Name:</strong> {file.name}</p>
                <p><strong>Type:</strong> {file.type}</p>
                <p><strong>Size:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <p><strong>Last Modified:</strong> {new Date(file.lastModified).toLocaleString()}</p>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <div class="card mt-4">
        <div class="card-header">
          <h5 class="mb-0">Features</h5>
        </div>
        <div class="card-body">
          <ul class="list-unstyled">
            <li class="mb-2">
              <i class="fas fa-mouse-pointer text-primary me-2"></i>
              <strong>Click to Select:</strong> Click anywhere in the drop zone to open file picker
            </li>
            <li class="mb-2">
              <i class="fas fa-hand-paper text-success me-2"></i>
              <strong>Drag & Drop:</strong> Drag image files directly onto the drop zone
            </li>
            <li class="mb-2">
              <i class="fas fa-clipboard text-info me-2"></i>
              <strong>Paste (Ctrl+V):</strong> Copy an image and paste it when the component is visible
            </li>
            <li class="mb-2">
              <i class="fas fa-edit text-warning me-2"></i>
              <strong>Image Editor:</strong> Edit images after selection with crop, resize, and filters
            </li>
            <li class="mb-2">
              <i class="fas fa-check-circle text-success me-2"></i>
              <strong>Validation:</strong> Automatic file type and size validation
            </li>
            <li class="mb-2">
              <i class="fas fa-mobile-alt text-secondary me-2"></i>
              <strong>Responsive:</strong> Optimized for mobile and desktop devices
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
