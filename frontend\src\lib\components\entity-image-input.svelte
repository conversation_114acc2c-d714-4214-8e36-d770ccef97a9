<script lang="ts">
  import type { Common } from "@commune/api";

  import { Consts } from "@commune/api";
  import { ImageEditor } from "$lib/components";
  import { onMount } from "svelte";

  interface Props {
    locale: Common.WebsiteLocale;
    file: File | null;
    disabled: boolean;
  }

  const MAX_FILE_SIZE_MB = Consts.MAX_IMAGE_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      dragDropText: "Drag & drop an image here",
      orText: "or",
      clickToSelect: "click to select",
      pasteText: "You can also paste (Ctrl+V)",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      supportedFormats: `Supported: JPG, PNG, WebP • Max ${MAX_FILE_SIZE_MB}MB`,
      editImage: "Edit",
      removeImage: "Remove",
      changeImage: "Change",
    },
    ru: {
      dragDropText: "Перетащите изображение сюда",
      orText: "или",
      clickToSelect: "нажмите для выбора",
      pasteText: "Также можно вставить (Ctrl+V)",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      supportedFormats: `Поддерживается: JPG, PNG, WebP • Макс ${MAX_FILE_SIZE_MB}MB`,
      editImage: "Редактировать",
      removeImage: "Удалить",
      changeImage: "Изменить",
    },
  };

  let { file = $bindable(), ...props }: Props = $props();

  const { locale, disabled = false } = props;

  const t = $derived(i18n[locale]);

  let previewUrl = $state<string | null>(null);
  let error = $state("");
  let showImageEditor = $state(false);
  let isDragOver = $state(false);
  let isProcessing = $state(false);

  let imageInput = $state<HTMLInputElement | null>(null);
  let dropZone = $state<HTMLDivElement | null>(null);

  function changeFile(newFile: File | null) {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }

    if (newFile) {
      file = newFile;
      previewUrl = URL.createObjectURL(newFile);
    } else {
      file = null;
      previewUrl = null;
    }
  }

  function validateFile(file: File): string | null {
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
      return t.invalidFileTypeError;
    }

    if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
      return t.fileTooLarge;
    }

    return null;
  }

  function processFile(file: File) {
    if (disabled) return;

    error = "";
    isProcessing = true;

    const validationError = validateFile(file);
    if (validationError) {
      error = validationError;
      isProcessing = false;
      return;
    }

    changeFile(file);
    showImageEditor = true;
    isProcessing = false;
  }

  const handleFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const files = target.files;

    if (!files || files.length === 0) {
      changeFile(null);
      return;
    }

    processFile(files[0]);
    // Clear the input so the same file can be selected again
    target.value = "";
  };

  const handleDragOver = (e: DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    isDragOver = true;
  };

  const handleDragLeave = (e: DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    isDragOver = false;
  };

  const handleDrop = (e: DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    isDragOver = false;

    const files = e.dataTransfer?.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  const handlePaste = (e: ClipboardEvent) => {
    if (disabled) return;

    const items = e.clipboardData?.items;
    if (!items) return;

    for (const item of items) {
      if (item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (file) {
          e.preventDefault();
          processFile(file);
          break;
        }
      }
    }
  };

  const handleImageEditorSave = (savedFile: File) => {
    changeFile(savedFile);
    showImageEditor = false;
  };

  const handleImageEditorCancel = () => {
    showImageEditor = false;
  };

  const handleEditImage = () => {
    if (file) {
      showImageEditor = true;
    }
  };

  const handleRemoveImage = () => {
    changeFile(null);
    showImageEditor = false;
    error = "";

    if (imageInput) {
      imageInput.value = "";
    }
  };

  const handleClickToSelect = () => {
    if (disabled) return;
    imageInput?.click();
  };

  onMount(() => {
    // Add global paste event listener
    const handleGlobalPaste = (e: ClipboardEvent) => {
      // Only handle paste if the drop zone is in view and no input is focused
      if (
        dropZone &&
        !showImageEditor &&
        !previewUrl &&
        document.activeElement?.tagName !== "INPUT" &&
        document.activeElement?.tagName !== "TEXTAREA"
      ) {
        handlePaste(e);
      }
    };

    document.addEventListener("paste", handleGlobalPaste);

    return () => {
      document.removeEventListener("paste", handleGlobalPaste);
    };
  });
</script>

{#if showImageEditor && file}
  <ImageEditor
    imageFile={file}
    onSave={handleImageEditorSave}
    onCancel={handleImageEditorCancel}
    {locale}
  />
{:else}
  <div class="entity-image-input">
    <!-- Hidden file input -->
    <input
      bind:this={imageInput}
      type="file"
      accept=".jpg,.jpeg,.png,.webp"
      onchange={handleFileChange}
      {disabled}
      style="display: none;"
    />

    {#if previewUrl && !showImageEditor}
      <!-- Preview Mode -->
      <div class="preview-container">
        <div class="image-preview">
          <img src={previewUrl} alt="Preview" />
          <div class="image-overlay">
            <div class="overlay-actions">
              <button
                type="button"
                class="action-btn edit-btn"
                onclick={handleEditImage}
                {disabled}
                title={t.editImage}
              >
                <i class="fas fa-edit"></i>
              </button>
              <button
                type="button"
                class="action-btn change-btn"
                onclick={handleClickToSelect}
                {disabled}
                title={t.changeImage}
              >
                <i class="fas fa-image"></i>
              </button>
              <button
                type="button"
                class="action-btn remove-btn"
                onclick={handleRemoveImage}
                {disabled}
                title={t.removeImage}
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    {:else}
      <!-- Upload Mode -->
      <div
        bind:this={dropZone}
        class="drop-zone"
        class:drag-over={isDragOver}
        class:disabled
        class:processing={isProcessing}
        ondragover={handleDragOver}
        ondragleave={handleDragLeave}
        ondrop={handleDrop}
        onclick={handleClickToSelect}
        role="button"
        tabindex="0"
        onkeydown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            handleClickToSelect();
          }
        }}
      >
        <div class="drop-zone-content">
          {#if isProcessing}
            <div class="processing-indicator">
              <i class="fas fa-spinner fa-spin"></i>
              <span>Processing...</span>
            </div>
          {:else}
            <div class="upload-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">
              <span class="primary-text">{t.dragDropText}</span>
              <span class="secondary-text">
                {t.orText} <span class="link-text">{t.clickToSelect}</span>
              </span>
              <span class="paste-text">{t.pasteText}</span>
            </div>
          {/if}
        </div>

        <div class="format-info">
          {t.supportedFormats}
        </div>
      </div>
    {/if}

    {#if error}
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        {error}
      </div>
    {/if}
  </div>
{/if}

<style>
  .entity-image-input {
    width: 100%;
  }

  .drop-zone {
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    outline: none;
  }

  .drop-zone:hover:not(.disabled) {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.02);
  }

  .drop-zone:focus:not(.disabled) {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }

  .drop-zone.drag-over {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
    transform: scale(1.02);
  }

  .drop-zone.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f8f9fa;
  }

  .drop-zone.processing {
    pointer-events: none;
  }

  .drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
  }

  .upload-text {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .primary-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
  }

  .secondary-text {
    font-size: 0.9rem;
    color: #6c757d;
  }

  .link-text {
    color: #007bff;
    text-decoration: underline;
  }

  .paste-text {
    font-size: 0.8rem;
    color: #adb5bd;
    margin-top: 0.5rem;
  }

  .processing-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #007bff;
  }

  .processing-indicator i {
    font-size: 2rem;
  }

  .format-info {
    position: absolute;
    bottom: 0.75rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: #adb5bd;
    white-space: nowrap;
  }

  .preview-container {
    position: relative;
    display: inline-block;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .image-preview {
    position: relative;
    max-width: 100%;
    max-height: 300px;
    overflow: hidden;
  }

  .image-preview img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
  }

  .image-preview:hover .image-overlay {
    opacity: 1;
  }

  .overlay-actions {
    display: flex;
    gap: 0.75rem;
  }

  .action-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
  }

  .action-btn:hover:not(:disabled) {
    background: white;
    transform: scale(1.1);
  }

  .action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .edit-btn:hover:not(:disabled) {
    color: #007bff;
  }

  .change-btn:hover:not(:disabled) {
    color: #28a745;
  }

  .remove-btn:hover:not(:disabled) {
    color: #dc3545;
  }

  .error-message {
    margin-top: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 8px;
    color: #dc3545;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .error-message i {
    flex-shrink: 0;
  }

  @media (max-width: 576px) {
    .drop-zone {
      padding: 1.5rem 1rem;
      min-height: 160px;
    }

    .upload-icon {
      font-size: 2.5rem;
    }

    .primary-text {
      font-size: 1rem;
    }

    .overlay-actions {
      gap: 0.5rem;
    }

    .action-btn {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }
  }
</style>
