<script lang="ts">
  import type { Common } from "@commune/api";

  import { Consts } from "@commune/api";
  import { ImageEditor } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    file: File | null;
    disabled: boolean;
  }

  const MAX_FILE_SIZE_MB = Consts.MAX_IMAGE_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      pleaseSelectImage: "Please select an image",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
    },
    ru: {
      pleaseSelectImage: "Пожалуйста, выберите изображение",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
    },
  };

  let { file = $bindable(), ...props }: Props = $props();

  const { locale, disabled = false } = props;

  const t = $derived(i18n[locale]);

  let previewUrl = $state<string | null>(null);
  let error = $state("");
  let showImageEditor = $state(false);

  let imageInput = $state<HTMLInputElement | null>(null);

  function changeFile(newFile: File | null) {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }

    if (newFile) {
      file = newFile;
      previewUrl = URL.createObjectURL(newFile);
    } else {
      file = null;
      previewUrl = null;
    }
  }

  const handleFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const files = target.files;

    error = "";

    if (!files || files.length === 0) {
      changeFile(null);
      return;
    }

    const probablyFile = files[0];

    // Validate file type
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(probablyFile.type)) {
      error = t.invalidFileTypeError;
      changeFile(null);
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    // Validate file size
    if (probablyFile.size > Consts.MAX_IMAGE_FILE_SIZE) {
      error = t.fileTooLarge;
      changeFile(null);
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    changeFile(probablyFile);
    showImageEditor = true;
  };

  const handleImageEditorSave = (savedFile: File) => {
    changeFile(savedFile);
    showImageEditor = false;
  };

  const handleImageEditorCancel = () => {
    showImageEditor = false;
  };

  const handleEditImage = () => {
    if (file) {
      showImageEditor = true;
    }
  };

  const handleSelectDifferentImage = () => {
    changeFile(null);
    showImageEditor = false;
    error = "";

    if (imageInput) {
      imageInput.value = "";
    }
  };
</script>

{#if showImageEditor && file}
  <ImageEditor
    imageFile={file}
    onSave={handleImageEditorSave}
    onCancel={handleImageEditorCancel}
    {locale}
  />
{:else}
  <div class="mb-3">
    <label class="form-label"
      >{t.pleaseSelectImage}

      <input
        bind:this={imageInput}
        type="file"
        class="form-control"
        accept=".jpg,.jpeg,.png,.webp"
        onchange={handleFileChange}
        {disabled}
      />
    </label>

    <p class="form-text text-muted">{t.uploadImageMaxSize}</p>

    {#if error}
      <div class="alert alert-danger mt-2 mb-0">
        {error}
      </div>
    {/if}

    {#if previewUrl && !showImageEditor}
      <div class="mt-3">
        <div class="text-center mb-3">
          <img src={previewUrl} alt="Preview" class="img-thumbnail" style:max-height="200px" />
        </div>

        <div class="d-flex gap-2 justify-content-center">
          <button
            type="button"
            class="btn btn-outline-primary btn-sm"
            onclick={handleEditImage}
            {disabled}
          >
            <i class="fas fa-edit"></i>
            {t.editImage}
          </button>
          <button
            type="button"
            class="btn btn-outline-secondary btn-sm"
            onclick={handleSelectDifferentImage}
            {disabled}
          >
            <i class="fas fa-image"></i>
            {t.selectDifferentImage}
          </button>
        </div>
      </div>
    {/if}
  </div>
{/if}
